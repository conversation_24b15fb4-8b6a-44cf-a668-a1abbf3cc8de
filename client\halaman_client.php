<?php
require '../koneksi.php';

// Ambil data tugas proyek untuk ditampilkan ke client
$query = "SELECT * FROM tugas_proyek ORDER BY tgl DESC";
$result = mysqli_query($koneksi, $query);

// Hitung statistik
$query_total = "SELECT COUNT(*) as total FROM tugas_proyek";
$result_total = mysqli_query($koneksi, $query_total);
$total_tugas = mysqli_fetch_array($result_total)['total'];

$query_selesai = "SELECT COUNT(*) as selesai FROM tugas_proyek WHERE status='selesai'";
$result_selesai = mysqli_query($koneksi, $query_selesai);
$tugas_selesai = mysqli_fetch_array($result_selesai)['selesai'];

$query_proses = "SELECT COUNT(*) as proses FROM tugas_proyek WHERE status='proses'";
$result_proses = mysqli_query($koneksi, $query_proses);
$tugas_proses = mysqli_fetch_array($result_proses)['proses'];

$query_batal = "SELECT COUNT(*) as batal FROM tugas_proyek WHERE status='batal'";
$result_batal = mysqli_query($koneksi, $query_batal);
$tugas_batal = mysqli_fetch_array($result_batal)['batal'];

// Hitung persentase progress
$progress_percentage = $total_tugas > 0 ? round(($tugas_selesai / $total_tugas) * 100, 1) : 0;
?>

<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Dashboard Client</h1>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Total Tugas Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Tugas</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_tugas; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tugas Selesai Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Tugas Selesai</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $tugas_selesai; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tugas Dalam Proses Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Tugas Dalam Proses
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800"><?php echo $tugas_proses; ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-spinner fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Keseluruhan Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Progress Keseluruhan</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $progress_percentage; ?>%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Area Chart -->
    <div class="col-xl-12 col-lg-12">
        <div class="card shadow mb-4">
            <!-- Card Header - Dropdown -->
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Progress Proyek</h6>
            </div>
            <!-- Card Body -->
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Nama Kegiatan</th>
                                <th>Deskripsi</th>
                                <th>Tanggal</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            while($row = mysqli_fetch_array($result)) { 
                                // Tentukan class badge berdasarkan status
                                $badge_class = '';
                                switch($row['status']) {
                                    case 'selesai':
                                        $badge_class = 'badge-success';
                                        break;
                                    case 'proses':
                                        $badge_class = 'badge-info';
                                        break;
                                    case 'batal':
                                        $badge_class = 'badge-danger';
                                        break;
                                    default:
                                        $badge_class = 'badge-secondary';
                                }
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['nama_kegiatan']); ?></td>
                                <td><?php echo htmlspecialchars($row['deskripsi']); ?></td>
                                <td><?php echo date('d/m/Y', strtotime($row['tgl'])); ?></td>
                                <td>
                                    <span class="badge <?php echo $badge_class; ?>">
                                        <?php echo ucfirst($row['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
